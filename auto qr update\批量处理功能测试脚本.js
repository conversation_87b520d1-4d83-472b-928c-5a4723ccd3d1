/**
 * AutoQR_Injector.js v3.1 批量处理功能测试脚本
 * 用于验证批量处理功能是否正常工作
 */

// ====================================================================================
// 测试脚本 - 请在浏览器控制台中运行
// ====================================================================================

console.log('🧪 开始测试 AutoQR_Injector.js v3.1 批量处理功能...');

// 测试1: 检查页面上的Translate按钮
function testTranslateButtonDetection() {
    console.log('\n📋 测试1: 检测页面上的Translate按钮');
    
    const translateButtons = document.querySelectorAll('div.row.mx-0.col-md-12 a[onclick*="qrCodeSubTranslate"]');
    console.log(`发现 ${translateButtons.length} 个Translate按钮`);
    
    if (translateButtons.length > 0) {
        console.log('✅ 按钮检测成功');
        
        // 显示前5个按钮的信息
        const sampleButtons = Array.from(translateButtons).slice(0, 5);
        sampleButtons.forEach((btn, index) => {
            const match = btn.getAttribute('onclick').match(/qrCodeSubTranslate\('(\d+)'\)/);
            const subId = match ? match[1] : 'unknown';
            console.log(`  ${index + 1}. sub_id: ${subId}`);
        });
        
        if (translateButtons.length > 5) {
            console.log(`  ... 还有 ${translateButtons.length - 5} 个按钮`);
        }
    } else {
        console.log('❌ 未找到任何Translate按钮，请确认页面已正确加载');
    }
    
    return translateButtons.length;
}

// 测试2: 检查AutoQR实例是否存在
function testAutoQRInstance() {
    console.log('\n📋 测试2: 检查AutoQR实例');
    
    if (typeof autoQR !== 'undefined') {
        console.log('✅ autoQR实例存在');
        console.log(`版本: ${autoQR.version}`);
        
        // 检查批量处理方法是否存在
        const methods = [
            'batchProcessAllServices',
            'processAllServicesStandard',
            'processAllServicesMain',
            'processAllServicesBasic'
        ];
        
        methods.forEach(method => {
            if (typeof autoQR[method] === 'function') {
                console.log(`✅ 方法 ${method} 存在`);
            } else {
                console.log(`❌ 方法 ${method} 不存在`);
            }
        });
        
        return true;
    } else {
        console.log('❌ autoQR实例不存在，请先加载AutoQR_Injector.js');
        return false;
    }
}

// 测试3: 模拟批量处理（仅测试，不实际执行）
function testBatchProcessingSimulation() {
    console.log('\n📋 测试3: 批量处理模拟测试');
    
    if (typeof autoQR === 'undefined') {
        console.log('❌ 跳过测试，autoQR实例不存在');
        return false;
    }
    
    // 模拟调用（不实际执行）
    console.log('🔧 模拟调用示例:');
    console.log('  await autoQR.processAllServicesStandard();');
    console.log('  await autoQR.processAllServicesMain();');
    console.log('  await autoQR.processAllServicesBasic();');
    console.log('  await autoQR.batchProcessAllServices([\'en\', \'zh-CN\']);');
    
    console.log('✅ 模拟测试完成');
    return true;
}

// 执行所有测试
async function runAllTests() {
    console.log('🚀 开始执行所有测试...\n');
    
    const buttonCount = testTranslateButtonDetection();
    const autoQRExists = testAutoQRInstance();
    const simulationTest = testBatchProcessingSimulation();
    
    console.log('\n📊 测试结果总结:');
    console.log(`  发现按钮数量: ${buttonCount}`);
    console.log(`  AutoQR实例: ${autoQRExists ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`  功能模拟: ${simulationTest ? '✅ 通过' : '❌ 失败'}`);
    
    if (buttonCount > 0 && autoQRExists) {
        console.log('\n🎉 所有测试通过！可以使用批量处理功能。');
        console.log('\n💡 使用建议:');
        console.log('  1. 先尝试少量语言: await autoQR.processAllServicesBasic()');
        console.log('  2. 确认无误后使用全部语言: await autoQR.processAllServicesStandard()');
        console.log('  3. 自定义语言组合: await autoQR.batchProcessAllServices([\'en\', \'zh-CN\', \'ms\'])');
    } else {
        console.log('\n⚠️ 测试未完全通过，请检查:');
        if (buttonCount === 0) {
            console.log('  - 页面是否已正确加载？');
            console.log('  - 是否在正确的QR码管理页面？');
        }
        if (!autoQRExists) {
            console.log('  - 是否已加载AutoQR_Injector.js脚本？');
            console.log('  - 脚本是否正确执行？');
        }
    }
}

// 快速测试函数
function quickTest() {
    console.log('⚡ 快速测试...');
    const buttonCount = document.querySelectorAll('div.row.mx-0.col-md-12 a[onclick*="qrCodeSubTranslate"]').length;
    const autoQRExists = typeof autoQR !== 'undefined';
    
    console.log(`按钮: ${buttonCount}, AutoQR: ${autoQRExists ? '✅' : '❌'}`);
    
    if (buttonCount > 0 && autoQRExists) {
        console.log('🎉 准备就绪！可以开始批量处理。');
    } else {
        console.log('⚠️ 请检查页面和脚本加载状态。');
    }
}

// ====================================================================================
// 使用说明
// ====================================================================================

console.log('\n📖 使用说明:');
console.log('1. 运行完整测试: runAllTests()');
console.log('2. 运行快速测试: quickTest()');
console.log('3. 检测按钮: testTranslateButtonDetection()');
console.log('4. 检查实例: testAutoQRInstance()');

// 自动运行快速测试
quickTest();
