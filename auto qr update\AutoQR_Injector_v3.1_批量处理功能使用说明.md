# AutoQR_Injector.js v3.1 批量处理功能使用说明

## 🚀 v3.1 新功能概述

AutoQR_Injector.js v3.1 新增了强大的**全页面批量处理功能**，可以自动发现并处理页面上所有需要翻译的服务，实现真正的一键式批量翻译管理。

## 🎯 核心功能

### **自动服务发现**
- 🔍 通过CSS选择器自动识别页面上所有Translate按钮
- 📊 精准定位：`div.row.mx-0.col-md-12 a[onclick*="qrCodeSubTranslate"]`
- 🎯 智能提取：从onclick属性中提取sub_id信息

### **智能批量处理**
- 🧠 结合v3.0的智能添加/编辑功能
- 📈 自动判断每个服务需要添加还是编辑翻译
- 🔄 逐个处理，确保每个服务都得到完整处理

### **完善的进度跟踪**
- 📊 实时显示处理进度
- ✅ 详细的成功/失败统计
- 🛡️ 单个服务失败不影响整体流程

## 📋 使用方法

### **1. 快速调用方法（推荐）**

#### **处理所有标准语言（10种）**
```javascript
await autoQR.processAllServicesStandard();
```
**包含语言**: id, ms, en, vi, ru, th, ja, zh-CN, zh-TW, ko

#### **处理主要语言（4种）**
```javascript
await autoQR.processAllServicesMain();
```
**包含语言**: en, zh-CN, ms, id

#### **处理基础语言（2种）**
```javascript
await autoQR.processAllServicesBasic();
```
**包含语言**: en, zh-CN

### **2. 自定义批量处理**

#### **基本调用**
```javascript
await autoQR.batchProcessAllServices(['en', 'zh-CN', 'ms']);
```

#### **带选项调用**
```javascript
await autoQR.batchProcessAllServices(
    ['en', 'zh-CN', 'ms', 'id'], 
    { 
        smartMode: true,           // 启用智能模式
        skipConfirmation: false    // 显示确认对话框
    }
);
```

### **3. 选项说明**

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `smartMode` | boolean | true | 启用智能添加/编辑判断 |
| `skipConfirmation` | boolean | false | 跳过用户确认对话框 |

## 🔧 处理流程

### **1. 服务发现阶段**
```
🔍 扫描页面 → 发现N个服务 → 用户确认 → 开始处理
```

### **2. 逐个处理阶段**
```
📋 处理服务 1/N → 点击Translate → 等待模态框 → 智能处理 → 下一个服务
```

### **3. 统计报告阶段**
```
📊 显示最终统计 → 成功率计算 → 失败服务详情
```

## 📊 输出示例

### **处理过程日志**
```
🚀 开始批量处理页面上所有翻译服务...
📊 发现 25 个服务需要处理
🎯 开始处理 25 个服务...

📋 处理服务 1/25 (sub_id: 613)...
✅ 服务 613 处理完成 (1/25)

📋 处理服务 2/25 (sub_id: 614)...
✅ 服务 614 处理完成 (2/25)

📊 进度更新: 10/25 已处理，成功 10，失败 0
...
```

### **最终统计报告**
```
🎉 批量处理完成:
  ✅ 成功: 23 个服务
  ❌ 失败: 2 个服务
  📊 总计: 25 个服务
  📈 成功率: 92.0%

❌ 失败服务详情:
  - sub_id: 618, 错误: 翻译模态框加载超时
  - sub_id: 622, 错误: 服务标题匹配失败
```

## 🛡️ 错误处理

### **常见错误类型**
1. **模态框加载超时**: 网络延迟或页面响应慢
2. **服务标题匹配失败**: translationDataSource中缺少对应服务
3. **翻译处理失败**: 表单填写或提交过程中的错误

### **错误恢复机制**
- ✅ 单个服务失败不影响其他服务处理
- ✅ 详细记录失败原因便于排查
- ✅ 可重复执行，支持断点续传

## ⚡ 性能优化

### **处理间隔**
- 每个服务处理完成后等待1.5秒
- 避免操作过快导致页面响应问题
- 确保模态框完全加载后再进行操作

### **进度显示**
- 每处理10个服务显示一次进度更新
- 实时显示成功/失败统计
- 避免日志过多影响性能

## 🎯 最佳实践

### **1. 使用前准备**
- ✅ 确保页面已完全加载
- ✅ 确认translationDataSource数据完整
- ✅ 建议在网络状况良好时使用

### **2. 推荐使用方式**
```javascript
// 推荐：使用标准语言处理
await autoQR.processAllServicesStandard();

// 或者：自定义语言组合
await autoQR.batchProcessAllServices(['en', 'zh-CN', 'ms', 'id']);
```

### **3. 故障排除**
- 如果某些服务处理失败，可以单独处理
- 检查失败服务的sub_id是否在translationDataSource中存在
- 确认页面元素结构是否发生变化

## 🔄 与其他功能的关系

### **与v3.0智能功能结合**
- 批量处理使用v3.0的智能添加/编辑判断
- 每个服务都会自动检测现有翻译状态
- 智能选择添加或编辑操作

### **与单个服务处理的区别**
- 批量处理：自动发现所有服务，逐个处理
- 单个处理：手动打开特定服务的翻译模态框

## 🎉 升级效果

AutoQR_Injector.js v3.1 现在是一个**完全自动化的翻译管理解决方案**：

1. **🚀 全自动化**: 一键处理页面上所有服务
2. **🧠 智能判断**: 自动识别添加还是编辑
3. **📊 详细统计**: 完整的处理报告和成功率
4. **🛡️ 错误恢复**: 单点失败不影响整体流程
5. **⚡ 高效处理**: 优化的处理间隔和进度显示

现在你可以真正实现"一键翻译所有服务"的梦想！
